package org.jeecg.modules.cw.mnlr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.enums.CwBaseKeyName;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.frdw.service.ICwFrdwService;
import org.jeecg.modules.cw.jscb.entity.CwJsZcb;
import org.jeecg.modules.cw.jscb.service.ICwJscbService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonth;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonthRow;
import org.jeecg.modules.cw.mnlr.mapper.CwMnlrMonthMapper;
import org.jeecg.modules.cw.mnlr.param.CwMnlrMonthSumbitParam;
import org.jeecg.modules.cw.mnlr.result.CwMnlrMonthQueryResult;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrMonthService;
import org.jeecg.modules.cw.qtfy.service.ICwQtfyService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 矿模拟利润（月）
 * @Author: jeecg-boot
 * @Date: 2025-01-07
 * @Version: V1.0
 */
@Service
public class CwMnlrMonthServiceImpl extends ServiceImpl<CwMnlrMonthMapper, CwMnlrMonth> implements ICwMnlrMonthService {

    private static final String DICT_TYPE = "mnlrMonth";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwQtfyService qtfyService;
    @Resource
    private ICwFrdwService frdwService;
    @Resource
    private ICwKBaseService baseDataService;
    @Resource
    private ICwMnlrDayService mnlrDayService;
    @Resource
    private ICwJscbService jscbService;

    @Override
    public CwMnlrMonthQueryResult queryByDate(Date queryDate) {
        CwMnlrMonthQueryResult result = new CwMnlrMonthQueryResult();
        queryDate = DateUtil.endOfMonth(queryDate);
        result.setQueryDate(queryDate);
        // 基础数据
        result.setQtfy(qtfyService.sumMonth(queryDate));
        result.setFrdw(frdwService.sumMonth(queryDate));
        String jh = baseDataService.getCwBaseDataYear(CwBaseKeyName.Year_JH, queryDate);
        String tzlr = baseDataService.getCwBaseDataMonth(CwBaseKeyName.Month_TZLR, queryDate);
        if (ObjectUtil.isNotEmpty(jh)) {
            result.setJh(new BigDecimal(jh));
        }
        if (ObjectUtil.isNotEmpty(tzlr)) {
            result.setTzlr(new BigDecimal(tzlr));
        }
        // 设置矿总成本（ZCB）
        BigDecimal zcb = baseDataService.getKzcb(DateUtil.endOfMonth(queryDate));
        result.setZcb(zcb);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);
                
        // 获取当月所有日模拟利润数据
        List<CwMnlrDay> dayDataList = mnlrDayService.lambdaQuery()
                .ge(CwMnlrDay::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwMnlrDay::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
                
        // 计算当月累计销量和加权平均价格
        Map<String, BigDecimal> typeToTotalXl = new HashMap<>();
        Map<String, BigDecimal> typeToTotalAmount = new HashMap<>();
        Map<String, BigDecimal> typeToValidXl = new HashMap<>();
        
        // 获取月累计成本
        List<CwJsZcb> monthCbList = jscbService.getJscb(DateUtil.endOfMonth(queryDate));
        Map<String, BigDecimal> typeToTotalCb = new HashMap<>();
        if (monthCbList != null) {
            for (CwJsZcb item : monthCbList) {
                if (item.getZcb() != null) {
                    typeToTotalCb.put(item.getType(), item.getZcb());
                }
            }
        }
        
        // 计算当月累计销量和总金额
        for (CwMnlrDay day : dayDataList) {
            String type = day.getType();
            BigDecimal xl = day.getXl() != null ? day.getXl() : BigDecimal.ZERO;
            BigDecimal jg = day.getJg() != null ? day.getJg() : BigDecimal.ZERO;
            
            // 累计销量
            typeToTotalXl.merge(type, xl, BigDecimal::add);
            
            // 累计金额(价格×销量)，只有当价格和销量都不为0时才计算
            if (xl.compareTo(BigDecimal.ZERO) > 0 && jg.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal amount = jg.multiply(xl);
                typeToTotalAmount.merge(type, amount, BigDecimal::add);
                
                // 记录有效销量（用于计算加权平均价格）
                typeToValidXl.merge(type, xl, BigDecimal::add);
            }
        }
        
        // 合并数据
        List<CwMnlrMonthRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwMnlrMonthRow row = new CwMnlrMonthRow();
            BeanUtil.copyProperties(d, row);

            // 设置累计销量
            String type = row.getType();
            BigDecimal totalXl = typeToTotalXl.getOrDefault(type, BigDecimal.ZERO);
            row.setXl(totalXl);
            
            // 设置加权平均价格
            BigDecimal validXl = typeToValidXl.getOrDefault(type, BigDecimal.ZERO);
            if (validXl.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal totalAmount = typeToTotalAmount.getOrDefault(type, BigDecimal.ZERO);
                BigDecimal avgPrice = totalAmount.divide(validXl, 3, RoundingMode.HALF_UP);
                row.setJg(avgPrice);
            }
            
            // 设置累计成本
            BigDecimal totalCb = typeToTotalCb.getOrDefault(type, BigDecimal.ZERO);
            row.setCb(totalCb);
            
            row.setXs(CwMnlrDay.getXs(row.getType()));
            // 添加
            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }

    @Override
    public void submit(CwMnlrMonthSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新基础数据
        if (ObjectUtil.isNotEmpty(param.getTzlr())) {
            baseDataService.setCwBaseDataMonth(CwBaseKeyName.Month_TZLR, param.getTzlr().toString(), submitDate);
        } else {
            baseDataService.deleteCwBaseData(CwBaseKeyName.Month_TZLR, submitDate, DateField.MONTH);
        }
        if (ObjectUtil.isNotEmpty(param.getJh())) {
            baseDataService.setCwBaseDataYear(CwBaseKeyName.Year_JH, param.getJh().toString(), submitDate);
        } else {
            baseDataService.deleteCwBaseData(CwBaseKeyName.Year_JH, submitDate, DateField.YEAR);
        }
    }
}
